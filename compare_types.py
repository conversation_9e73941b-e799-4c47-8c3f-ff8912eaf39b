#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to find additional types entries in chernarusplus that are not in enoch types.
"""

import xml.etree.ElementTree as ET

def extract_type_names(xml_file):
    """Extract all type names from an XML types file."""
    try:
        tree = ET.parse(xml_file)
        root = tree.getroot()
        
        type_names = set()
        for type_elem in root.findall('type'):
            name = type_elem.get('name')
            if name:
                type_names.add(name)
        
        return type_names
    except Exception as e:
        print(f"Error parsing {xml_file}: {e}")
        return set()

def main():
    # Extract type names from both files
    print("Extracting type names from chernarusplus128types.xml...")
    chernarusplus_types = extract_type_names('chernarusplus128types.xml')
    print(f"Found {len(chernarusplus_types)} types in chernarusplus")

    print("Extracting type names from enoch128types.xml...")
    enoch_types = extract_type_names('enoch128types.xml')
    print(f"Found {len(enoch_types)} types in enoch")

    # Find types that are in enoch but not in chernarusplus
    additional_types_enoch = enoch_types - chernarusplus_types

    print(f"\nFound {len(additional_types_enoch)} additional types in enoch that are not in chernarusplus:")
    print("=" * 70)

    # Sort the additional types for better readability
    for type_name in sorted(additional_types_enoch):
        print(type_name)

    # Also show some statistics
    print("\n" + "=" * 70)
    print("SUMMARY:")
    print(f"Total types in chernarusplus: {len(chernarusplus_types)}")
    print(f"Total types in enoch: {len(enoch_types)}")
    print(f"Additional types in enoch: {len(additional_types_enoch)}")
    print(f"Common types: {len(chernarusplus_types & enoch_types)}")

    # Also show the reverse comparison for completeness
    additional_types_chernarusplus = chernarusplus_types - enoch_types
    print(f"Additional types in chernarusplus: {len(additional_types_chernarusplus)}")

if __name__ == "__main__":
    main()
